<x-filament-panels::page @class(['fi-struktur-organisasi-detail-page'])>
    <style>
        /* BULLETPROOF FULL WIDTH SOLUTION */

        /* Maximum specificity CSS overrides */
        html body .fi-struktur-organisasi-detail-page .fi-main-ctn .fi-main,
        html body .fi-struktur-organisasi-detail-page .fi-main-ctn .fi-main.mx-auto,
        html body .fi-struktur-organisasi-detail-page .fi-main-ctn .fi-main.max-w-7xl,
        html body .fi-struktur-organisasi-detail-page .fi-main-ctn .fi-main.max-w-6xl,
        html body .fi-struktur-organisasi-detail-page .fi-main-ctn .fi-main.max-w-5xl,
        html body .fi-struktur-organisasi-detail-page .fi-main-ctn .fi-main.max-w-4xl,
        html body .fi-struktur-organisasi-detail-page .fi-main-ctn .fi-main.max-w-3xl,
        html body .fi-struktur-organisasi-detail-page .fi-main-ctn .fi-main.max-w-2xl,
        html body .fi-struktur-organisasi-detail-page .fi-main-ctn .fi-main.max-w-xl,
        html body .fi-struktur-organisasi-detail-page .fi-main-ctn .fi-main.max-w-full,
        html body .fi-struktur-organisasi-detail-page .fi-main-ctn .fi-main.max-w-screen-2xl,
        html body .fi-struktur-organisasi-detail-page .fi-main-ctn .fi-main.max-w-screen-xl,
        html body .fi-struktur-organisasi-detail-page .fi-main-ctn .fi-main.max-w-screen-lg {
            max-width: none !important;
            width: 100% !important;
            margin-left: 0 !important;
            margin-right: 0 !important;
            padding-left: 1rem !important;
            padding-right: 1rem !important;
        }

        /* Force container to use full width */
        html body .fi-struktur-organisasi-detail-page .fi-main-ctn {
            width: 100% !important;
            max-width: none !important;
        }

        /* Organizational tree container with maximum specificity */
        html body .fi-struktur-organisasi-detail-page .org-tree {
            width: 100% !important;
            max-width: none !important;
            margin: 0 !important;
            padding: 0 !important;
            box-sizing: border-box !important;
        }

        /* SCOPED FORCE WIDTH SOLUTION - Only for this specific page */
        body:has(.fi-struktur-organisasi-detail-page) .fi-struktur-organisasi-detail-page .fi-main-ctn {
            width: calc(100vw - 280px) !important;
            /* Force viewport-based width */
            max-width: none !important;
            min-width: calc(100vw - 280px) !important;
        }

        body:has(.fi-struktur-organisasi-detail-page) .fi-struktur-organisasi-detail-page .fi-main {
            width: calc(100vw - 312px) !important;
            /* Subtract sidebar + padding */
            max-width: none !important;
            min-width: calc(100vw - 312px) !important;
            margin-left: 0 !important;
            margin-right: 0 !important;
            padding-left: 1rem !important;
            padding-right: 1rem !important;
        }

        /* VIEWPORT-BASED FALLBACK */
        @supports not (display: grid) {
            .fi-struktur-organisasi-detail-page .fi-main {
                width: calc(100vw - 280px) !important;
                /* Subtract sidebar width */
                max-width: none !important;
                margin-left: 0 !important;
                margin-right: 0 !important;
            }
        }

        /* NUCLEAR OPTION - FORCE WIDTH WITH HIGHEST SPECIFICITY */
        html body div.fi-struktur-organisasi-detail-page div.fi-main-ctn main.fi-main {
            width: calc(100vw - 312px) !important;
            max-width: none !important;
            min-width: calc(100vw - 312px) !important;
        }

        /* RESPONSIVE ADJUSTMENTS - Scoped to this page only */
        @media (max-width: 1024px) {

            body:has(.fi-struktur-organisasi-detail-page) .fi-struktur-organisasi-detail-page .fi-main,
            body:has(.fi-struktur-organisasi-detail-page) html body div.fi-struktur-organisasi-detail-page div.fi-main-ctn main.fi-main {
                width: calc(100vw - 96px) !important;
                /* Collapsed sidebar + padding */
                min-width: calc(100vw - 96px) !important;
            }
        }

        @media (max-width: 768px) {

            body:has(.fi-struktur-organisasi-detail-page) .fi-struktur-organisasi-detail-page .fi-main,
            body:has(.fi-struktur-organisasi-detail-page) html body div.fi-struktur-organisasi-detail-page div.fi-main-ctn main.fi-main {
                width: calc(100vw - 32px) !important;
                /* No sidebar, just padding */
                min-width: calc(100vw - 32px) !important;
                padding-left: 1rem !important;
                padding-right: 1rem !important;
            }
        }

        /* Success! Full width organizational chart achieved */

        /* Ensure Filament containers use full available width */
        .fi-struktur-organisasi-detail-page .fi-main,
        .fi-struktur-organisasi-detail-page .fi-section-content-ctn,
        .fi-struktur-organisasi-detail-page .fi-section-content,
        .fi-struktur-organisasi-detail-page .fi-view-entry-content,
        .fi-struktur-organisasi-detail-page .fi-page {
            max-width: none !important;
            width: 100% !important;
        }

        /* Remove any Tailwind max-width constraints specifically for this page */
        .fi-struktur-organisasi-detail-page .fi-main.max-w-7xl,
        .fi-struktur-organisasi-detail-page .fi-main.max-w-6xl,
        .fi-struktur-organisasi-detail-page .fi-main.max-w-5xl,
        .fi-struktur-organisasi-detail-page .fi-main.max-w-4xl,
        .fi-struktur-organisasi-detail-page .fi-main.max-w-3xl,
        .fi-struktur-organisasi-detail-page .fi-main.max-w-2xl,
        .fi-struktur-organisasi-detail-page .fi-main.max-w-xl {
            max-width: none !important;
        }



        .search-highlight {
            @apply bg-yellow-100 px-1 py-0.5 rounded-sm;
        }

        .dark .search-highlight {
            @apply bg-yellow-800 text-yellow-200;
        }

        .hidden-by-search {
            display: none !important;
        }

        .org-chart-pattern::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E") repeat;
            opacity: 0.3;
        }

        .org-level-line::before {
            content: '';
            position: absolute;
            top: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 80%;
            height: 1px;
            background: rgba(255, 255, 255, 0.3);
        }





        .tree-view {
            display: none;
        }

        .tree-view.active {
            display: block;
        }

        .chart-view {
            display: none;
        }

        .chart-view.active {
            display: block;
        }

        .canvas-view {
            display: none;
        }

        .canvas-view.active {
            display: block;
        }

        /* Canvas specific styles */
        .canvas-container {
            position: relative;
            width: 100%;
            height: 80vh;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            overflow: hidden;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .canvas-controls {
            position: absolute;
            top: 20px;
            right: 20px;
            z-index: 10;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .canvas-control-btn {
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.9);
            border: none;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s;
            font-size: 18px;
        }

        .canvas-control-btn:hover {
            background: white;
            transform: scale(1.1);
        }

        .canvas-minimap {
            position: absolute;
            bottom: 20px;
            right: 20px;
            width: 200px;
            height: 150px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 8px;
            border: 2px solid #e5e7eb;
        }
    </style>

    <div class="space-y-6 org-tree">
        @php
            // Get all departments that have employees in this entity
            $departemen = \App\Models\Departemen::whereHas('karyawan', function ($query) use ($entitas) {
                $query->where('id_entitas', $entitas->id)->where('status_aktif', true);
            })
                ->with([
                    'karyawan' => function ($query) use ($entitas) {
                        $query
                            ->where('id_entitas', $entitas->id)
                            ->where('status_aktif', true)
                            ->with(['divisi', 'jabatan']);
                    },
                ])
                ->get();
        @endphp

        <!-- Summary Cards -->
        <div
            class="grid grid-cols-1 gap-5 p-5 mb-8 sm:grid-cols-2 lg:grid-cols-4 bg-white/5 dark:bg-gray-800/5 rounded-xl">
            <div class="p-5 text-center bg-white border-l-4 border-indigo-500 shadow-lg dark:bg-gray-800 rounded-xl">
                <div class="mb-1 text-3xl font-bold text-gray-900 dark:text-white">
                    {{ $entitas->karyawan()->where('status_aktif', true)->count() }}</div>
                <div class="text-sm text-gray-600 dark:text-gray-400">Total Karyawan</div>
            </div>
            <div class="p-5 text-center bg-white border-l-4 border-pink-500 shadow-lg dark:bg-gray-800 rounded-xl">
                <div class="mb-1 text-3xl font-bold text-gray-900 dark:text-white">{{ $departemen->count() }}</div>
                <div class="text-sm text-gray-600 dark:text-gray-400">Departemen</div>
            </div>
            <div class="p-5 text-center bg-white border-l-4 shadow-lg dark:bg-gray-800 rounded-xl border-cyan-500">
                @php
                    $totalDivisi = collect();
                    foreach ($departemen as $dept) {
                        $divisiGroups = $dept->karyawan->groupBy('id_divisi');
                        foreach ($divisiGroups as $divisiId => $karyawanInDivisi) {
                            $divisi = $karyawanInDivisi->first()->divisi;
                            if ($divisi && !$totalDivisi->contains('id', $divisi->id)) {
                                $totalDivisi->push($divisi);
                            }
                        }
                    }
                @endphp
                <div class="mb-1 text-3xl font-bold text-gray-900 dark:text-white">{{ $totalDivisi->count() }}</div>
                <div class="text-sm text-gray-600 dark:text-gray-400">Divisi</div>
            </div>
            <div class="p-5 text-center bg-white border-l-4 border-green-500 shadow-lg dark:bg-gray-800 rounded-xl">
                @php
                    $totalJabatan = collect();
                    foreach ($departemen as $dept) {
                        $divisiGroups = $dept->karyawan->groupBy('id_divisi');
                        foreach ($divisiGroups as $divisiId => $karyawanInDivisi) {
                            $jabatanGroups = $karyawanInDivisi->groupBy('id_jabatan');
                            foreach ($jabatanGroups as $jabatanId => $karyawanInJabatan) {
                                $jabatan = $karyawanInJabatan->first()->jabatan;
                                if ($jabatan && !$totalJabatan->contains('id', $jabatan->id)) {
                                    $totalJabatan->push($jabatan);
                                }
                            }
                        }
                    }
                @endphp
                <div class="mb-1 text-3xl font-bold text-gray-900 dark:text-white">{{ $totalJabatan->count() }}</div>
                <div class="text-sm text-gray-600 dark:text-gray-400">Jabatan</div>
            </div>
        </div>

        <!-- View Toggle -->
        <div class="flex justify-center mb-5 gap-2.5 md:flex-row flex-col items-center">
            <button
                class="toggle-btn px-5 py-2.5 border-2 border-blue-500 bg-white dark:bg-gray-800 text-blue-500 dark:text-blue-400 rounded-lg cursor-pointer transition-all duration-300 font-medium hover:-translate-y-0.5 hover:shadow-lg hover:shadow-blue-500/30 md:w-auto w-48 text-center"
                data-view="chart">
                📊 Tampilan Chart
            </button>
            <button
                class="toggle-btn px-5 py-2.5 border-2 border-blue-500 bg-blue-500 text-white rounded-lg cursor-pointer transition-all duration-300 font-medium hover:-translate-y-0.5 hover:shadow-lg hover:shadow-blue-500/30 md:w-auto w-48 text-center"
                data-view="canvas">
                🎨 Tampilan Canvas
            </button>
            <button
                class="toggle-btn px-5 py-2.5 border-2 border-blue-500 bg-white dark:bg-gray-800 text-blue-500 dark:text-blue-400 rounded-lg cursor-pointer transition-all duration-300 font-medium hover:-translate-y-0.5 hover:shadow-lg hover:shadow-blue-500/30 md:w-auto w-48 text-center"
                data-view="tree">
                🌳 Tampilan Tree
            </button>
        </div>

        <!-- Search Box -->
        <div class="mb-6">
            <div class="relative">
                <input type="text" id="orgSearch" placeholder="Cari karyawan, departemen, divisi, atau jabatan..."
                    class="w-full px-4 py-2 pl-10 pr-4 text-gray-700 bg-white border border-gray-300 rounded-lg dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                <div class="absolute inset-y-0 left-0 flex items-center pl-3">
                    <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                </div>
                <button id="clearSearch"
                    class="absolute inset-y-0 right-0 items-center hidden pr-3 text-gray-400 hover:text-gray-600">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12">
                        </path>
                    </svg>
                </button>
            </div>
        </div>

        <!-- Chart View -->
        <div class="chart-view active">
            <div
                class="flex flex-col items-center py-10 px-5 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl mb-8 relative overflow-visible min-h-[80vh] w-full org-chart-pattern">
                <div class="relative z-10 w-full">
                    <!-- Entitas Level -->
                    <div class="flex justify-center mb-8">
                        <div
                            class="bg-gradient-to-br from-indigo-500 to-purple-600 text-white text-xl font-bold max-w-md mx-auto mb-5 min-h-[150px] bg-white rounded-lg p-3 shadow-lg text-center w-full relative transition-all duration-300 border-2 border-transparent flex flex-col justify-start hover:-translate-y-1 hover:shadow-xl hover:border-blue-500">
                            <div class="text-3xl mb-2.5">🏢</div>
                            <div class="mb-1 font-bold">{{ $entitas->nama }}</div>
                            <div class="text-sm opacity-80">
                                {{ $entitas->karyawan()->where('status_aktif', true)->count() }} Karyawan</div>
                        </div>
                    </div>

                    @if ($departemen->count() > 0)
                        <div class="w-0.5 h-5 bg-white/50 mx-auto"></div>

                        <!-- Hierarchical Structure: Departemen → Divisi → Jabatan -->
                        @foreach ($departemen as $dept)
                            <!-- Departemen Level -->
                            <div class="flex justify-center mb-8">
                                <div
                                    class="bg-gradient-to-br from-pink-400 to-red-500 text-white bg-white rounded-lg p-3 shadow-lg text-center w-full min-h-[120px] relative transition-all duration-300 border-2 border-transparent flex flex-col justify-start hover:-translate-y-1 hover:shadow-xl hover:border-blue-500">
                                    <div class="text-3xl mb-2.5">📁</div>
                                    <div class="mb-1 font-bold">{{ $dept->nama_departemen }}</div>
                                    <div class="text-sm opacity-80">{{ $dept->karyawan->count() }} Karyawan</div>

                                    <button
                                        class="bg-white/20 border border-white/30 text-white px-3 py-1.5 rounded-md text-xs cursor-pointer mt-2.5 transition-all duration-300 hover:bg-white/30 hover:-translate-y-0.5"
                                        onclick="toggleEmployees('dept-{{ $dept->id }}')">
                                        Lihat Semua Karyawan
                                    </button>

                                    <div id="dept-{{ $dept->id }}" class="mt-4 overflow-y-auto text-left max-h-48"
                                        style="display: none;">
                                        @if ($dept->karyawan->count() > 0)
                                            @foreach ($dept->karyawan as $karyawan)
                                                <div
                                                    class="bg-white/90 rounded-lg p-2 px-3 mb-1.5 border-l-4 border-indigo-500 text-xs shadow-sm">
                                                    <div class="font-semibold text-gray-800 mb-0.5">
                                                        {{ $karyawan->nama_lengkap }}</div>
                                                    @if ($karyawan->nip)
                                                        <div class="text-xs italic text-gray-500">NIP:
                                                            {{ $karyawan->nip }}</div>
                                                    @endif
                                                    <div class="text-xs text-gray-600">
                                                        {{ $karyawan->jabatan ? $karyawan->jabatan->nama_jabatan : 'Jabatan tidak ditentukan' }}
                                                        @if ($karyawan->divisi)
                                                            - {{ $karyawan->divisi->nama_divisi }}
                                                        @endif
                                                    </div>
                                                </div>
                                            @endforeach
                                        @else
                                            <div class="text-gray-400 italic text-center p-2.5">Tidak ada karyawan</div>
                                        @endif
                                    </div>
                                </div>
                            </div>

                            @php
                                // Group employees by division within this department
                                $divisiGroups = $dept->karyawan->groupBy('id_divisi');
                            @endphp

                            @if ($divisiGroups->count() > 0)
                                <div class="w-0.5 h-5 bg-white/50 mx-auto"></div>

                                <!-- Divisi Level for this Department -->
                                <div
                                    class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-1.5 justify-center mb-8 relative w-full max-w-none mx-auto p-0 md:gap-1.5 md:grid-cols-3 lg:gap-1.5">
                                    @foreach ($divisiGroups as $divisiId => $karyawanInDivisi)
                                        @php
                                            $divisi = $karyawanInDivisi->first()->divisi;
                                        @endphp
                                        <div
                                            class="bg-gradient-to-br from-cyan-400 to-blue-500 text-white bg-white rounded-lg p-3 shadow-lg text-center w-full min-h-[120px] relative transition-all duration-300 border-2 border-transparent flex flex-col justify-start hover:-translate-y-1 hover:shadow-xl hover:border-blue-500">
                                            <div class="text-3xl mb-2.5">📂</div>
                                            <div class="mb-1 font-bold">
                                                {{ $divisi ? $divisi->nama_divisi : 'Divisi Tidak Ditentukan' }}
                                            </div>
                                            <div class="text-sm opacity-80">{{ $karyawanInDivisi->count() }} Karyawan
                                            </div>

                                            <button
                                                class="bg-white/20 border border-white/30 text-white px-3 py-1.5 rounded-md text-xs cursor-pointer mt-2.5 transition-all duration-300 hover:bg-white/30 hover:-translate-y-0.5"
                                                onclick="toggleEmployees('divisi-{{ $dept->id }}-{{ $divisiId }}')">
                                                Lihat Karyawan
                                            </button>

                                            <div id="divisi-{{ $dept->id }}-{{ $divisiId }}"
                                                class="mt-4 overflow-y-auto text-left max-h-48" style="display: none;">
                                                @foreach ($karyawanInDivisi as $karyawan)
                                                    <div
                                                        class="bg-white/90 rounded-lg p-2 px-3 mb-1.5 border-l-4 border-indigo-500 text-xs shadow-sm">
                                                        <div class="font-semibold text-gray-800 mb-0.5">
                                                            {{ $karyawan->nama_lengkap }}</div>
                                                        @if ($karyawan->nip)
                                                            <div class="text-xs italic text-gray-500">NIP:
                                                                {{ $karyawan->nip }}</div>
                                                        @endif
                                                        <div class="text-xs text-gray-600">
                                                            {{ $karyawan->jabatan ? $karyawan->jabatan->nama_jabatan : 'Jabatan tidak ditentukan' }}
                                                        </div>
                                                    </div>
                                                @endforeach
                                            </div>
                                        </div>
                                    @endforeach
                                </div>

                                @php
                                    // Group all positions within this department's divisions
$allJabatanInDept = collect();
foreach ($divisiGroups as $divisiId => $karyawanInDivisi) {
    $jabatanGroups = $karyawanInDivisi->groupBy('id_jabatan');
    foreach ($jabatanGroups as $jabatanId => $karyawanInJabatan) {
        $jabatan = $karyawanInJabatan->first()->jabatan;
        if ($jabatan && !$allJabatanInDept->contains('id', $jabatan->id)) {
            $allJabatanInDept->push([
                'jabatan' => $jabatan,
                'divisi_id' => $divisiId,
                'karyawan' => $karyawanInJabatan,
                                                ]);
                                            }
                                        }
                                    }
                                @endphp

                                @if ($allJabatanInDept->count() > 0)
                                    <div class="w-0.5 h-5 bg-white/50 mx-auto"></div>

                                    <!-- Jabatan Level for this Department -->
                                    <div
                                        class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-1.5 justify-center mb-8 relative w-full max-w-none mx-auto p-0 md:gap-1.5 md:grid-cols-3 lg:gap-1.5">
                                        @foreach ($allJabatanInDept as $jabatanData)
                                            <div
                                                class="bg-gradient-to-br from-green-400 to-emerald-500 text-gray-800 bg-white rounded-lg p-3 shadow-lg text-center w-full min-h-[120px] relative transition-all duration-300 border-2 border-transparent flex flex-col justify-start hover:-translate-y-1 hover:shadow-xl hover:border-blue-500">
                                                <div class="text-3xl mb-2.5">🏷️</div>
                                                <div class="mb-1 font-bold">{{ $jabatanData['jabatan']->nama_jabatan }}
                                                </div>
                                                <div class="text-sm opacity-80">{{ $jabatanData['karyawan']->count() }}
                                                    Karyawan</div>

                                                <button
                                                    class="bg-white/20 border border-white/30 text-gray-800 px-3 py-1.5 rounded-md text-xs cursor-pointer mt-2.5 transition-all duration-300 hover:bg-white/30 hover:-translate-y-0.5"
                                                    onclick="toggleEmployees('jabatan-{{ $dept->id }}-{{ $jabatanData['jabatan']->id }}')">
                                                    Lihat Karyawan
                                                </button>

                                                <div id="jabatan-{{ $dept->id }}-{{ $jabatanData['jabatan']->id }}"
                                                    class="mt-4 overflow-y-auto text-left max-h-48"
                                                    style="display: none;">
                                                    @foreach ($jabatanData['karyawan'] as $karyawan)
                                                        <div
                                                            class="bg-white/90 rounded-lg p-2 px-3 mb-1.5 border-l-4 border-indigo-500 text-xs shadow-sm">
                                                            <div class="font-semibold text-gray-800 mb-0.5">
                                                                {{ $karyawan->nama_lengkap }}</div>
                                                            @if ($karyawan->nip)
                                                                <div class="text-xs italic text-gray-500">NIP:
                                                                    {{ $karyawan->nip }}</div>
                                                            @endif
                                                            <div class="text-xs text-gray-600">
                                                                @if ($karyawan->divisi)
                                                                    {{ $karyawan->divisi->nama_divisi }}
                                                                @else
                                                                    Divisi tidak ditentukan
                                                                @endif
                                                            </div>
                                                        </div>
                                                    @endforeach
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                @endif
                            @endif

                            @if (!$loop->last)
                                <div class="w-0.5 h-10 bg-white/50 mx-auto my-10"></div>
                            @endif
                        @endforeach
                    @endif
                </div>
            </div>
        </div>

        <!-- Canvas View -->
        <div class="canvas-view">
            <div class="canvas-container">
                <canvas id="orgCanvas" width="1200" height="800"></canvas>

                <!-- Canvas Controls -->
                <div class="canvas-controls">
                    <button class="canvas-control-btn" id="zoomIn" title="Zoom In">
                        ➕
                    </button>
                    <button class="canvas-control-btn" id="zoomOut" title="Zoom Out">
                        ➖
                    </button>
                    <button class="canvas-control-btn" id="fitToScreen" title="Fit to Screen">
                        🔍
                    </button>
                    <button class="canvas-control-btn" id="resetView" title="Reset View">
                        🏠
                    </button>
                </div>

                <!-- Minimap -->
                <div class="canvas-minimap">
                    <canvas id="minimap" width="200" height="150"></canvas>
                </div>
            </div>
        </div>

        <!-- Tree View -->
        <div class="tree-view">
            @if ($departemen->count() > 0)
                @foreach ($departemen as $dept)
                    <div
                        class="p-6 bg-white border border-gray-200 rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700 transition-all duration-300 hover:-translate-y-0.5 hover:shadow-xl">
                        <!-- Department Header -->
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
                                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                                    {{ $dept->nama_departemen }}
                                </h3>
                            </div>
                            <span
                                class="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs font-medium px-2.5 py-0.5 rounded">
                                {{ $dept->karyawan->count() }} Karyawan
                            </span>
                        </div>

                        @php
                            // Group employees by division
                            $divisiGroups = $dept->karyawan->groupBy('id_divisi');
                        @endphp

                        @foreach ($divisiGroups as $divisiId => $karyawanInDivisi)
                            @php
                                $divisi = $karyawanInDivisi->first()->divisi;
                            @endphp

                            <div
                                class="pl-4 mb-4 ml-6 border-l-2 border-gray-200 dark:border-gray-600 relative before:content-[''] before:absolute before:-left-px before:top-0 before:bottom-0 before:w-0.5 before:bg-gradient-to-b before:from-gray-300 before:to-gray-400">
                                <!-- Division Header -->
                                <div class="flex items-center justify-between mb-3">
                                    <div class="flex items-center space-x-2">
                                        <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                                        <h4 class="font-medium text-gray-800 text-md dark:text-gray-200">
                                            {{ $divisi ? $divisi->nama_divisi : 'Divisi Tidak Ditentukan' }}
                                        </h4>
                                    </div>
                                    <span
                                        class="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 text-xs font-medium px-2 py-0.5 rounded">
                                        {{ $karyawanInDivisi->count() }} Karyawan
                                    </span>
                                </div>

                                @php
                                    // Group employees by position
                                    $jabatanGroups = $karyawanInDivisi->groupBy('id_jabatan');
                                @endphp

                                @foreach ($jabatanGroups as $jabatanId => $karyawanInJabatan)
                                    @php
                                        $jabatan = $karyawanInJabatan->first()->jabatan;
                                    @endphp

                                    <div class="pl-3 mb-3 ml-4 border-l border-gray-300 dark:border-gray-500">
                                        <!-- Position Header -->
                                        <div class="flex items-center justify-between mb-2">
                                            <div class="flex items-center space-x-2">
                                                <div class="w-1.5 h-1.5 bg-yellow-500 rounded-full"></div>
                                                <h5 class="text-sm font-medium text-gray-700 dark:text-gray-300">
                                                    {{ $jabatan ? $jabatan->nama_jabatan : 'Jabatan Tidak Ditentukan' }}
                                                </h5>
                                            </div>
                                            <span
                                                class="bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 text-xs px-2 py-0.5 rounded">
                                                {{ $karyawanInJabatan->count() }}
                                            </span>
                                        </div>

                                        <!-- Employees List -->
                                        <div class="ml-3 space-y-1">
                                            @foreach ($karyawanInJabatan as $karyawan)
                                                <div
                                                    class="flex items-center justify-between px-2 py-1 text-sm transition-all duration-200 border-l-4 border-transparent rounded bg-gray-50 dark:bg-gray-700 hover:border-l-blue-500 hover:bg-slate-50 dark:hover:bg-slate-800">
                                                    <div class="flex items-center space-x-2">
                                                        <div class="w-1 h-1 bg-gray-400 rounded-full"></div>
                                                        <span class="font-medium text-gray-800 dark:text-gray-200">
                                                            {{ $karyawan->nama_lengkap }}
                                                        </span>
                                                        @if ($karyawan->nip)
                                                            <span class="text-xs text-gray-500 dark:text-gray-400">
                                                                ({{ $karyawan->nip }})
                                                            </span>
                                                        @endif
                                                    </div>
                                                    <div class="flex items-center space-x-2">
                                                        @if ($karyawan->supervisor_id)
                                                            <span
                                                                class="bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 text-xs px-1.5 py-0.5 rounded">
                                                                👥 Supervised
                                                            </span>
                                                        @endif
                                                        @if ($karyawan->email)
                                                            <span
                                                                class="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs px-1.5 py-0.5 rounded">
                                                                📧 Email
                                                            </span>
                                                        @endif
                                                        <span
                                                            class="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 text-xs px-1.5 py-0.5 rounded">
                                                            ✅ Aktif
                                                        </span>
                                                    </div>
                                                </div>
                                            @endforeach
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        @endforeach
                    </div>
                @endforeach
            @else
                <div class="py-12 text-center">
                    <div
                        class="flex items-center justify-center w-24 h-24 mx-auto mb-4 bg-gray-100 rounded-full dark:bg-gray-700">
                        <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor"
                            viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4">
                            </path>
                        </svg>
                    </div>
                    <h3 class="mb-2 text-lg font-medium text-gray-900 dark:text-white">Tidak Ada Data Organisasi</h3>
                    <p class="text-gray-500 dark:text-gray-400">Belum ada karyawan yang terdaftar di entitas ini.</p>
                </div>
            @endif
        </div>

        <script>
            // Toggle employee list visibility
            function toggleEmployees(elementId) {
                const element = document.getElementById(elementId);
                const button = element.previousElementSibling;

                if (element.style.display === 'none' || element.style.display === '') {
                    element.style.display = 'block';
                    button.textContent = 'Sembunyikan Karyawan';
                } else {
                    element.style.display = 'none';
                    button.textContent = 'Lihat Karyawan';
                }
            }

            // Canvas Organizational Chart Implementation
            class CanvasOrgChart {
                constructor(canvasId, minimapId, data) {
                    this.canvas = document.getElementById(canvasId);
                    this.ctx = this.canvas.getContext('2d');
                    this.minimap = document.getElementById(minimapId);
                    this.minimapCtx = this.minimap.getContext('2d');

                    this.data = data;
                    this.nodes = [];
                    this.connections = [];

                    // View state
                    this.scale = 1;
                    this.offsetX = 0;
                    this.offsetY = 0;
                    this.isDragging = false;
                    this.lastMouseX = 0;
                    this.lastMouseY = 0;

                    // Node dimensions
                    this.nodeWidth = 200;
                    this.nodeHeight = 120;
                    this.levelSpacing = 250;
                    this.nodeSpacing = 50;

                    this.init();
                }

                init() {
                    this.setupCanvas();
                    this.processData();
                    this.setupEventListeners();
                    this.render();
                }

                setupCanvas() {
                    const container = this.canvas.parentElement;
                    this.canvas.width = container.clientWidth;
                    this.canvas.height = container.clientHeight;

                    // Set initial view to center
                    this.offsetX = this.canvas.width / 2;
                    this.offsetY = 100;
                }

                processData() {
                    this.nodes = [];
                    this.connections = [];

                    let nodeId = 0;
                    let currentY = 0;

                    // Add entitas node (root)
                    const entitasNode = {
                        id: nodeId++,
                        type: 'entitas',
                        name: this.data.entitas.nama,
                        count: this.data.entitas.total_karyawan,
                        x: 0,
                        y: currentY,
                        level: 0,
                        color: '#667eea'
                    };
                    this.nodes.push(entitasNode);

                    currentY += this.levelSpacing;

                    // Process departments
                    let deptX = -(this.data.departemen.length - 1) * (this.nodeWidth + this.nodeSpacing) / 2;

                    this.data.departemen.forEach((dept, deptIndex) => {
                        const deptNode = {
                            id: nodeId++,
                            type: 'departemen',
                            name: dept.nama_departemen,
                            count: dept.karyawan_count,
                            x: deptX + deptIndex * (this.nodeWidth + this.nodeSpacing),
                            y: currentY,
                            level: 1,
                            color: '#ec4899',
                            parentId: entitasNode.id
                        };
                        this.nodes.push(deptNode);

                        // Add connection
                        this.connections.push({
                            from: entitasNode.id,
                            to: deptNode.id
                        });

                        // Process divisions for this department
                        if (dept.divisi && dept.divisi.length > 0) {
                            let divisiY = currentY + this.levelSpacing;
                            let divisiX = deptNode.x - (dept.divisi.length - 1) * (this.nodeWidth + this
                                .nodeSpacing) / 2;

                            dept.divisi.forEach((divisi, divisiIndex) => {
                                const divisiNode = {
                                    id: nodeId++,
                                    type: 'divisi',
                                    name: divisi.nama_divisi,
                                    count: divisi.karyawan_count,
                                    x: divisiX + divisiIndex * (this.nodeWidth + this.nodeSpacing),
                                    y: divisiY,
                                    level: 2,
                                    color: '#06b6d4',
                                    parentId: deptNode.id
                                };
                                this.nodes.push(divisiNode);

                                this.connections.push({
                                    from: deptNode.id,
                                    to: divisiNode.id
                                });

                                // Process jabatan for this divisi
                                if (divisi.jabatan && divisi.jabatan.length > 0) {
                                    let jabatanY = divisiY + this.levelSpacing;
                                    let jabatanX = divisiNode.x - (divisi.jabatan.length - 1) * (this
                                        .nodeWidth + this.nodeSpacing) / 2;

                                    divisi.jabatan.forEach((jabatan, jabatanIndex) => {
                                        const jabatanNode = {
                                            id: nodeId++,
                                            type: 'jabatan',
                                            name: jabatan.nama_jabatan,
                                            count: jabatan.karyawan_count,
                                            x: jabatanX + jabatanIndex * (this.nodeWidth + this
                                                .nodeSpacing),
                                            y: jabatanY,
                                            level: 3,
                                            color: '#10b981',
                                            parentId: divisiNode.id
                                        };
                                        this.nodes.push(jabatanNode);

                                        this.connections.push({
                                            from: divisiNode.id,
                                            to: jabatanNode.id
                                        });
                                    });
                                }
                            });
                        }
                    });
                }

                setupEventListeners() {
                    // Mouse events
                    this.canvas.addEventListener('mousedown', (e) => this.onMouseDown(e));
                    this.canvas.addEventListener('mousemove', (e) => this.onMouseMove(e));
                    this.canvas.addEventListener('mouseup', (e) => this.onMouseUp(e));
                    this.canvas.addEventListener('wheel', (e) => this.onWheel(e));

                    // Touch events for mobile
                    this.canvas.addEventListener('touchstart', (e) => this.onTouchStart(e));
                    this.canvas.addEventListener('touchmove', (e) => this.onTouchMove(e));
                    this.canvas.addEventListener('touchend', (e) => this.onTouchEnd(e));

                    // Control buttons
                    document.getElementById('zoomIn').addEventListener('click', () => this.zoomIn());
                    document.getElementById('zoomOut').addEventListener('click', () => this.zoomOut());
                    document.getElementById('fitToScreen').addEventListener('click', () => this.fitToScreen());
                    document.getElementById('resetView').addEventListener('click', () => this.resetView());

                    // Window resize
                    window.addEventListener('resize', () => this.onResize());
                }

                onMouseDown(e) {
                    this.isDragging = true;
                    const rect = this.canvas.getBoundingClientRect();
                    this.lastMouseX = e.clientX - rect.left;
                    this.lastMouseY = e.clientY - rect.top;
                    this.canvas.style.cursor = 'grabbing';
                }

                onMouseMove(e) {
                    if (!this.isDragging) return;

                    const rect = this.canvas.getBoundingClientRect();
                    const mouseX = e.clientX - rect.left;
                    const mouseY = e.clientY - rect.top;

                    this.offsetX += mouseX - this.lastMouseX;
                    this.offsetY += mouseY - this.lastMouseY;

                    this.lastMouseX = mouseX;
                    this.lastMouseY = mouseY;

                    this.render();
                }

                onMouseUp(e) {
                    this.isDragging = false;
                    this.canvas.style.cursor = 'grab';
                }

                onWheel(e) {
                    e.preventDefault();

                    const rect = this.canvas.getBoundingClientRect();
                    const mouseX = e.clientX - rect.left;
                    const mouseY = e.clientY - rect.top;

                    const zoomFactor = e.deltaY > 0 ? 0.9 : 1.1;
                    const newScale = Math.max(0.1, Math.min(3, this.scale * zoomFactor));

                    // Zoom towards mouse position
                    this.offsetX = mouseX - (mouseX - this.offsetX) * (newScale / this.scale);
                    this.offsetY = mouseY - (mouseY - this.offsetY) * (newScale / this.scale);

                    this.scale = newScale;
                    this.render();
                }

                onTouchStart(e) {
                    e.preventDefault();
                    if (e.touches.length === 1) {
                        this.isDragging = true;
                        const rect = this.canvas.getBoundingClientRect();
                        this.lastMouseX = e.touches[0].clientX - rect.left;
                        this.lastMouseY = e.touches[0].clientY - rect.top;
                    }
                }

                onTouchMove(e) {
                    e.preventDefault();
                    if (e.touches.length === 1 && this.isDragging) {
                        const rect = this.canvas.getBoundingClientRect();
                        const touchX = e.touches[0].clientX - rect.left;
                        const touchY = e.touches[0].clientY - rect.top;

                        this.offsetX += touchX - this.lastMouseX;
                        this.offsetY += touchY - this.lastMouseY;

                        this.lastMouseX = touchX;
                        this.lastMouseY = touchY;

                        this.render();
                    }
                }

                onTouchEnd(e) {
                    e.preventDefault();
                    this.isDragging = false;
                }

                zoomIn() {
                    this.scale = Math.min(3, this.scale * 1.2);
                    this.render();
                }

                zoomOut() {
                    this.scale = Math.max(0.1, this.scale * 0.8);
                    this.render();
                }

                fitToScreen() {
                    if (this.nodes.length === 0) return;

                    // Calculate bounds
                    let minX = Infinity,
                        maxX = -Infinity;
                    let minY = Infinity,
                        maxY = -Infinity;

                    this.nodes.forEach(node => {
                        minX = Math.min(minX, node.x - this.nodeWidth / 2);
                        maxX = Math.max(maxX, node.x + this.nodeWidth / 2);
                        minY = Math.min(minY, node.y - this.nodeHeight / 2);
                        maxY = Math.max(maxY, node.y + this.nodeHeight / 2);
                    });

                    const contentWidth = maxX - minX;
                    const contentHeight = maxY - minY;

                    const scaleX = (this.canvas.width - 100) / contentWidth;
                    const scaleY = (this.canvas.height - 100) / contentHeight;

                    this.scale = Math.min(scaleX, scaleY, 1);

                    // Center the content
                    this.offsetX = this.canvas.width / 2 - (minX + maxX) / 2 * this.scale;
                    this.offsetY = this.canvas.height / 2 - (minY + maxY) / 2 * this.scale;

                    this.render();
                }

                resetView() {
                    this.scale = 1;
                    this.offsetX = this.canvas.width / 2;
                    this.offsetY = 100;
                    this.render();
                }

                onResize() {
                    const container = this.canvas.parentElement;
                    this.canvas.width = container.clientWidth;
                    this.canvas.height = container.clientHeight;
                    this.render();
                }

                render() {
                    // Clear canvas
                    this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

                    // Save context
                    this.ctx.save();

                    // Apply transformations
                    this.ctx.translate(this.offsetX, this.offsetY);
                    this.ctx.scale(this.scale, this.scale);

                    // Draw connections first
                    this.drawConnections();

                    // Draw nodes
                    this.drawNodes();

                    // Restore context
                    this.ctx.restore();

                    // Update minimap
                    this.updateMinimap();
                }

                drawConnections() {
                    this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.6)';
                    this.ctx.lineWidth = 2;

                    this.connections.forEach(conn => {
                        const fromNode = this.nodes.find(n => n.id === conn.from);
                        const toNode = this.nodes.find(n => n.id === conn.to);

                        if (fromNode && toNode) {
                            this.ctx.beginPath();
                            this.ctx.moveTo(fromNode.x, fromNode.y + this.nodeHeight / 2);
                            this.ctx.lineTo(toNode.x, toNode.y - this.nodeHeight / 2);
                            this.ctx.stroke();
                        }
                    });
                }

                drawNodes() {
                    this.nodes.forEach(node => {
                        this.drawNode(node);
                    });
                }

                drawNode(node) {
                    const x = node.x - this.nodeWidth / 2;
                    const y = node.y - this.nodeHeight / 2;

                    // Create gradient
                    const gradient = this.ctx.createLinearGradient(x, y, x + this.nodeWidth, y + this.nodeHeight);
                    gradient.addColorStop(0, node.color);
                    gradient.addColorStop(1, this.darkenColor(node.color, 20));

                    // Draw node background
                    this.ctx.fillStyle = gradient;
                    this.ctx.beginPath();
                    this.ctx.roundRect(x, y, this.nodeWidth, this.nodeHeight, 12);
                    this.ctx.fill();

                    // Draw border
                    this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
                    this.ctx.lineWidth = 2;
                    this.ctx.stroke();

                    // Draw icon
                    this.ctx.fillStyle = 'white';
                    this.ctx.font = '24px Arial';
                    this.ctx.textAlign = 'center';
                    const icon = this.getNodeIcon(node.type);
                    this.ctx.fillText(icon, node.x, y + 35);

                    // Draw title
                    this.ctx.font = 'bold 14px Arial';
                    this.ctx.fillStyle = 'white';
                    this.ctx.textAlign = 'center';

                    // Wrap text if too long
                    const maxWidth = this.nodeWidth - 20;
                    const words = node.name.split(' ');
                    let line = '';
                    let lineY = y + 60;

                    for (let n = 0; n < words.length; n++) {
                        const testLine = line + words[n] + ' ';
                        const metrics = this.ctx.measureText(testLine);
                        const testWidth = metrics.width;

                        if (testWidth > maxWidth && n > 0) {
                            this.ctx.fillText(line, node.x, lineY);
                            line = words[n] + ' ';
                            lineY += 16;
                        } else {
                            line = testLine;
                        }
                    }
                    this.ctx.fillText(line, node.x, lineY);

                    // Draw count
                    this.ctx.font = '12px Arial';
                    this.ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
                    this.ctx.fillText(`${node.count} Karyawan`, node.x, y + this.nodeHeight - 15);
                }

                getNodeIcon(type) {
                    const icons = {
                        'entitas': '🏢',
                        'departemen': '📁',
                        'divisi': '📂',
                        'jabatan': '🏷️'
                    };
                    return icons[type] || '📄';
                }

                darkenColor(color, percent) {
                    const num = parseInt(color.replace("#", ""), 16);
                    const amt = Math.round(2.55 * percent);
                    const R = (num >> 16) + amt;
                    const G = (num >> 8 & 0x00FF) + amt;
                    const B = (num & 0x0000FF) + amt;
                    return "#" + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
                        (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
                        (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1);
                }

                updateMinimap() {
                    if (!this.minimap) return;

                    const minimapCtx = this.minimapCtx;
                    minimapCtx.clearRect(0, 0, this.minimap.width, this.minimap.height);

                    if (this.nodes.length === 0) return;

                    // Calculate bounds
                    let minX = Infinity,
                        maxX = -Infinity;
                    let minY = Infinity,
                        maxY = -Infinity;

                    this.nodes.forEach(node => {
                        minX = Math.min(minX, node.x - this.nodeWidth / 2);
                        maxX = Math.max(maxX, node.x + this.nodeWidth / 2);
                        minY = Math.min(minY, node.y - this.nodeHeight / 2);
                        maxY = Math.max(maxY, node.y + this.nodeHeight / 2);
                    });

                    const contentWidth = maxX - minX;
                    const contentHeight = maxY - minY;

                    const scaleX = this.minimap.width / contentWidth;
                    const scaleY = this.minimap.height / contentHeight;
                    const minimapScale = Math.min(scaleX, scaleY) * 0.8;

                    const offsetX = (this.minimap.width - contentWidth * minimapScale) / 2;
                    const offsetY = (this.minimap.height - contentHeight * minimapScale) / 2;

                    minimapCtx.save();
                    minimapCtx.translate(offsetX - minX * minimapScale, offsetY - minY * minimapScale);
                    minimapCtx.scale(minimapScale, minimapScale);

                    // Draw nodes on minimap
                    this.nodes.forEach(node => {
                        minimapCtx.fillStyle = node.color;
                        minimapCtx.fillRect(
                            node.x - this.nodeWidth / 2,
                            node.y - this.nodeHeight / 2,
                            this.nodeWidth,
                            this.nodeHeight
                        );
                    });

                    minimapCtx.restore();

                    // Draw viewport indicator
                    const viewportX = (-this.offsetX / this.scale + minX) * minimapScale + offsetX;
                    const viewportY = (-this.offsetY / this.scale + minY) * minimapScale + offsetY;
                    const viewportWidth = (this.canvas.width / this.scale) * minimapScale;
                    const viewportHeight = (this.canvas.height / this.scale) * minimapScale;

                    minimapCtx.strokeStyle = '#ff0000';
                    minimapCtx.lineWidth = 2;
                    minimapCtx.strokeRect(viewportX, viewportY, viewportWidth, viewportHeight);
                }

                searchNode(searchTerm) {
                    const foundNode = this.nodes.find(node =>
                        node.name.toLowerCase().includes(searchTerm.toLowerCase())
                    );

                    if (foundNode) {
                        // Center view on found node
                        this.offsetX = this.canvas.width / 2 - foundNode.x * this.scale;
                        this.offsetY = this.canvas.height / 2 - foundNode.y * this.scale;
                        this.render();
                        return true;
                    }
                    return false;
                }
            }

            // Initialize canvas when data is available
            let canvasChart = null;

            document.addEventListener('DOMContentLoaded', function() {
                        console.log('🔍 DEBUGGING: Starting full-width implementation...');

                        // BULLETPROOF FULL WIDTH IMPLEMENTATION
                        function bulletproofFullWidth() {
                            console.log('🔧 Executing bulletproof full width...');

                            // 1. Find ALL possible container elements
                            const selectors = [
                                '.fi-struktur-organisasi-detail-page .fi-main',
                                '.fi-struktur-organisasi-detail-page .fi-main-ctn',
                                '.fi-main',
                                '.fi-main-ctn',
                                '.fi-page',
                                '.fi-section-content',
                                '.fi-section-content-ctn'
                            ];

                            selectors.forEach(selector => {
                                const elements = document.querySelectorAll(selector);
                                elements.forEach(element => {
                                    // Log current state
                                    const computedStyle = window.getComputedStyle(element);
                                    console.log(`📊 Element: ${selector}`, {
                                        maxWidth: computedStyle.maxWidth,
                                        width: computedStyle.width,
                                        classes: element.className
                                    });

                                    // AGGRESSIVE CLASS REMOVAL
                                    const allMaxWidthClasses = [
                                        'max-w-xs', 'max-w-sm', 'max-w-md', 'max-w-lg', 'max-w-xl',
                                        'max-w-2xl', 'max-w-3xl', 'max-w-4xl', 'max-w-5xl',
                                        'max-w-6xl', 'max-w-7xl', 'max-w-full', 'max-w-screen-sm',
                                        'max-w-screen-md', 'max-w-screen-lg', 'max-w-screen-xl',
                                        'max-w-screen-2xl', 'max-w-none', 'max-w-min', 'max-w-max',
                                        'max-w-fit', 'max-w-prose', 'mx-auto'
                                    ];

                                    allMaxWidthClasses.forEach(className => {
                                        if (element.classList.contains(className)) {
                                            console.log(
                                                `🗑️ Removing class: ${className} from ${selector}`
                                            );
                                            element.classList.remove(className);
                                        }
                                    });

                                    // FORCE STYLES WITH MAXIMUM PRIORITY
                                    element.style.setProperty('max-width', 'none', 'important');
                                    element.style.setProperty('width', '100%', 'important');
                                    element.style.setProperty('margin-left', '0', 'important');
                                    element.style.setProperty('margin-right', '0', 'important');

                                    // ULTIMATE FIX: Force specific pixel width based on viewport
                                    const sidebar = document.querySelector('.fi-sidebar');
                                    const sidebarWidth = sidebar ? sidebar.offsetWidth : 280;
                                    const availableWidth = window.innerWidth - sidebarWidth -
                                        32; // 32px for padding

                                    console.log(`🎯 Forcing width to ${availableWidth}px for ${selector}`);
                                    element.style.setProperty('width', `${availableWidth}px`, 'important');
                                    element.style.setProperty('min-width', `${availableWidth}px`,
                                        'important');

                                    // Success - full width applied
                                });
                            });

                            // 2. Special handling for org-tree
                            const orgTree = document.querySelector('.org-tree');
                            if (orgTree) {
                                console.log('🌳 Processing org-tree...');
                                orgTree.style.setProperty('width', '100%', 'important');
                                orgTree.style.setProperty('max-width', 'none', 'important');
                                orgTree.style.setProperty('margin', '0', 'important');
                                orgTree.style.setProperty('padding', '0 1rem', 'important');
                            }

                            // 3. Log final state
                            const mainContainer = document.querySelector('.fi-main');
                            if (mainContainer) {
                                const finalStyle = window.getComputedStyle(mainContainer);
                                console.log('✅ Final main container state:', {
                                    maxWidth: finalStyle.maxWidth,
                                    width: finalStyle.width,
                                    marginLeft: finalStyle.marginLeft,
                                    marginRight: finalStyle.marginRight,
                                    classes: mainContainer.className
                                });
                            }
                        }

                        // AGGRESSIVE EXECUTION STRATEGY
                        console.log('🚀 Starting aggressive execution...');

                        // Immediate execution
                        bulletproofFullWidth();

                        // Multiple delayed executions
                        setTimeout(bulletproofFullWidth, 50);
                        setTimeout(bulletproofFullWidth, 100);
                        setTimeout(bulletproofFullWidth, 250);
                        setTimeout(bulletproofFullWidth, 500);
                        setTimeout(bulletproofFullWidth, 1000);
                        setTimeout(bulletproofFullWidth, 2000);

                        // Watch for DOM changes
                        const observer = new MutationObserver(function(mutations) {
                            let shouldReapply = false;
                            mutations.forEach(function(mutation) {
                                if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                                    console.log('🔄 Class change detected, reapplying full width...');
                                    shouldReapply = true;
                                }
                            });
                            if (shouldReapply) {
                                setTimeout(bulletproofFullWidth, 10);
                            }
                        });

                        // Observe the main container for changes
                        const mainContainer = document.querySelector('.fi-main');
                        if (mainContainer) {
                            observer.observe(mainContainer, {
                                attributes: true,
                                attributeFilter: ['class', 'style'],
                                subtree: true
                            });
                        }

                        // Window resize handler
                        window.addEventListener('resize', function() {
                            console.log('📐 Window resized, reapplying full width...');
                            setTimeout(bulletproofFullWidth, 100);
                        });

                        console.log('✅ Full width implementation completed!');

                        // EMERGENCY VIEWPORT-BASED SOLUTION
                        function emergencyFullWidth() {
                            console.log('🚨 Applying emergency viewport-based solution...');

                            const orgTree = document.querySelector('.org-tree');
                            const mainContainer = document.querySelector('.fi-main');

                            if (orgTree && mainContainer) {
                                // Get sidebar width
                                const sidebar = document.querySelector('.fi-sidebar');
                                const sidebarWidth = sidebar ? sidebar.offsetWidth : 280; // Default Filament sidebar width

                                // Calculate available width
                                const availableWidth = window.innerWidth - sidebarWidth - 32; // 32px for padding

                                console.log(
                                    `📐 Viewport: ${window.innerWidth}px, Sidebar: ${sidebarWidth}px, Available: ${availableWidth}px`
                                );

                                // Apply emergency styles
                                orgTree.style.setProperty('width', `${availableWidth}px`, 'important');
                                orgTree.style.setProperty('max-width', 'none', 'important');
                                orgTree.style.setProperty('position', 'relative', 'important');
                                orgTree.style.setProperty('left', '0', 'important');
                                orgTree.style.setProperty('margin', '0', 'important');
                                orgTree.style.setProperty('padding', '0 1rem', 'important');

                                // Force parent containers
                                mainContainer.style.setProperty('overflow-x', 'visible', 'important');
                                mainContainer.style.setProperty('width', '100%', 'important');
                                mainContainer.style.setProperty('max-width', 'none', 'important');

                                console.log('🚨 Emergency solution applied!');
                            }
                        }

                        // Apply emergency solution immediately AND as last resort
                        emergencyFullWidth();
                        setTimeout(emergencyFullWidth, 100);
                        setTimeout(emergencyFullWidth, 500);
                        setTimeout(emergencyFullWidth, 1000);
                        setTimeout(emergencyFullWidth, 3000);

                        // Success! Full width organizational chart achieved

                        const searchInput = document.getElementById('orgSearch');
                        const clearButton = document.getElementById('clearSearch');
                        const orgTree = document.querySelector('.org-tree');

                        // Prepare data for canvas
                        const canvasData = {
                            entitas: {
                                nama: @json($entitas->nama),
                                total_karyawan: {{ $entitas->karyawan()->where('status_aktif', true)->count() }}
                            },
                            departemen: [
                                @foreach ($departemen as $dept)
                                    {
                                        nama_departemen: @json($dept->nama_departemen),
                                        karyawan_count: {{ $dept->karyawan->count() }},
                                        divisi: [
                                            @php
                                                $divisiGroups = $dept->karyawan->groupBy('id_divisi');
                                            @endphp
                                            @foreach ($divisiGroups as $divisiId => $karyawanInDivisi)
                                                @php
                                                    $divisi = $karyawanInDivisi->first()->divisi;
                                                    $jabatanGroups = $karyawanInDivisi->groupBy('id_jabatan');
                                                @endphp {
                                                    nama_divisi: @json($divisi ? $divisi->nama_divisi : 'Divisi Tidak Ditentukan'),
                                                    karyawan_count: {{ $karyawanInDivisi->count() }},
                                                    jabatan: [
                                                        @foreach ($jabatanGroups as $jabatanId => $karyawanInJabatan)
                                                            @php
                                                                $jabatan = $karyawanInJabatan->first()->jabatan;
                                                            @endphp {
                                                                nama_jabatan: @json($jabatan ? $jabatan->nama_jabatan : 'Jabatan Tidak Ditentukan'),
                                                                karyawan_count: {{ $karyawanInJabatan->count() }}
                                                            }
                                                            @if (!$loop->last)
                                                                ,
                                                            @endif
                                                        @endforeach
                                                    ]
                                                }
                                                @if (!$loop->last)
                                                    ,
                                                @endif
                                            @endforeach
                                        ]
                                    }
                                    @if (!$loop->last)
                                        ,
                                    @endif
                                @endforeach
                            ]
                        };

                        // View Toggle Functionality - Wrap in DOM ready
                        document.addEventListener('DOMContentLoaded', function() {
                            const toggleButtons = document.querySelectorAll('.toggle-btn');
                            const chartView = document.querySelector('.chart-view');
                            const canvasView = document.querySelector('.canvas-view');
                            const treeView = document.querySelector('.tree-view');

                            toggleButtons.forEach(button => {
                                button.addEventListener('click', function() {
                                    const viewType = this.getAttribute('data-view');

                                    // Update button states
                                    toggleButtons.forEach(btn => {
                                        btn.classList.remove('bg-blue-500', 'text-white');
                                        btn.classList.add('bg-white', 'dark:bg-gray-800',
                                            'text-blue-500',
                                            'dark:text-blue-400');
                                    });
                                    this.classList.remove('bg-white', 'dark:bg-gray-800',
                                        'text-blue-500',
                                        'dark:text-blue-400');
                                    this.classList.add('bg-blue-500', 'text-white');

                                    // Hide all views
                                    chartView.classList.remove('active');
                                    canvasView.classList.remove('active');
                                    treeView.classList.remove('active');

                                    // Show selected view
                                    setTimeout(() => {
                                        if (viewType === 'chart') {
                                            chartView.classList.add('active');
                                        } else if (viewType === 'canvas') {
                                            canvasView.classList.add('active');
                                            // Initialize canvas if not already done
                                            if (!canvasChart) {
                                                canvasChart = new CanvasOrgChart('orgCanvas',
                                                    'minimap',
                                                    canvasData);
                                            }
                                        } else if (viewType === 'tree') {
                                            treeView.classList.add('active');
                                        }
                                    }, 100);
                                });
                            });

                            // Set default view (chart view)
                            const defaultButton = document.querySelector('.toggle-btn[data-view="chart"]');
                            if (defaultButton) {
                                defaultButton.classList.remove('bg-white', 'dark:bg-gray-800', 'text-blue-500',
                                    'dark:text-blue-400');
                                defaultButton.classList.add('bg-blue-500', 'text-white');
                                chartView.classList.add('active');
                            }

                            if (!searchInput || !orgTree) return;

                            // Search functionality
                            searchInput.addEventListener('input', function() {
                                const searchTerm = this.value.toLowerCase().trim();

                                if (searchTerm === '') {
                                    clearSearch();
                                    return;
                                }

                                // Show clear button
                                clearButton.classList.remove('hidden');
                                clearButton.classList.add('flex');

                                // Check if canvas view is active
                                if (canvasView.classList.contains('active') && canvasChart) {
                                    const found = canvasChart.searchNode(searchTerm);
                                    if (!found) {
                                        // Show no results message for canvas
                                        console.log('No results found in canvas view');
                                    }
                                    return;
                                }

                                // Get all searchable elements
                                const departments = orgTree.querySelectorAll('.org-node');
                                let hasVisibleResults = false;

                                departments.forEach(dept => {
                                    let deptHasMatch = false;
                                    const deptName = dept.querySelector('h3').textContent.toLowerCase();

                                    // Check department name
                                    if (deptName.includes(searchTerm)) {
                                        deptHasMatch = true;
                                        highlightText(dept.querySelector('h3'), searchTerm);
                                    }

                                    // Check divisions and positions
                                    const divisions = dept.querySelectorAll('.org-connector');
                                    divisions.forEach(div => {
                                        let divHasMatch = false;
                                        const divName = div.querySelector('h4').textContent
                                            .toLowerCase();

                                        // Check division name
                                        if (divName.includes(searchTerm)) {
                                            divHasMatch = true;
                                            deptHasMatch = true;
                                            highlightText(div.querySelector('h4'), searchTerm);
                                        }

                                        // Check positions
                                        const positions = div.querySelectorAll('h5');
                                        positions.forEach(pos => {
                                            let posHasMatch = false;
                                            const posName = pos.textContent
                                            .toLowerCase();

                                            if (posName.includes(searchTerm)) {
                                                posHasMatch = true;
                                                divHasMatch = true;
                                                deptHasMatch = true;
                                                highlightText(pos, searchTerm);
                                            }

                                            // Check employees
                                            const positionContainer = pos.closest(
                                                '.border-l');
                                            const employees = positionContainer
                                                .querySelectorAll(
                                                    '.employee-card');
                                            let positionHasVisibleEmployees = false;

                                            employees.forEach(emp => {
                                                const empName = emp
                                                    .querySelector(
                                                        '.font-medium')
                                                    .textContent
                                                    .toLowerCase();
                                                const empNip = emp
                                                    .querySelector(
                                                        '.text-xs')
                                                    ?.textContent
                                                .toLowerCase() || '';

                                                if (empName.includes(
                                                    searchTerm) ||
                                                    empNip
                                                    .includes(searchTerm)) {
                                                    emp.classList.remove(
                                                        'hidden-by-search');
                                                    highlightText(emp
                                                        .querySelector(
                                                            '.font-medium'),
                                                        searchTerm);
                                                    positionHasVisibleEmployees
                                                        = true;
                                                    posHasMatch = true;
                                                    divHasMatch = true;
                                                    deptHasMatch = true;
                                                } else if (!posHasMatch) {
                                                    emp.classList.add(
                                                        'hidden-by-search');
                                                } else {
                                                    emp.classList.remove(
                                                        'hidden-by-search');
                                                }
                                            });

                                            // Hide position if no matches
                                            if (!posHasMatch && !
                                                positionHasVisibleEmployees) {
                                                positionContainer.classList.add(
                                                    'hidden-by-search');
                                            } else {
                                                positionContainer.classList.remove(
                                                    'hidden-by-search');
                                            }
                                        });

                                        // Hide division if no matches
                                        if (!divHasMatch) {
                                            div.classList.add('hidden-by-search');
                                        } else {
                                            div.classList.remove('hidden-by-search');
                                        }
                                    });

                                    // Hide department if no matches
                                    if (!deptHasMatch) {
                                        dept.classList.add('hidden-by-search');
                                    } else {
                                        dept.classList.remove('hidden-by-search');
                                        hasVisibleResults = true;
                                    }
                                });

                                // Show no results message if needed
                                showNoResultsMessage(!hasVisibleResults);
                            });

                            // Clear search
                            clearButton.addEventListener('click', clearSearch);

                            function clearSearch() {
                                searchInput.value = '';
                                clearButton.classList.add('hidden');
                                clearButton.classList.remove('flex');

                                // Check if canvas view is active
                                if (canvasView.classList.contains('active') && canvasChart) {
                                    // Reset canvas view to original position
                                    canvasChart.resetView();
                                    return;
                                }

                                // Remove all highlights and hidden classes for tree/chart view
                                orgTree.querySelectorAll('.search-highlight').forEach(el => {
                                    el.outerHTML = el.innerHTML;
                                });

                                orgTree.querySelectorAll('.hidden-by-search').forEach(el => {
                                    el.classList.remove('hidden-by-search');
                                });

                                // Remove no results message
                                const noResults = document.getElementById('noSearchResults');
                                if (noResults) noResults.remove();
                            }

                            function highlightText(element, searchTerm) {
                                if (!element) return;

                                const text = element.textContent;
                                const regex = new RegExp(`(${searchTerm})`, 'gi');
                                const highlightedText = text.replace(regex, '<span class="search-highlight">$1</span>');
                                element.innerHTML = highlightedText;
                            }

                            function showNoResultsMessage(show) {
                                const existingMessage = document.getElementById('noSearchResults');

                                if (show && !existingMessage) {
                                    const message = document.createElement('div');
                                    message.id = 'noSearchResults';
                                    message.className = 'text-center py-12';
                                    message.innerHTML = `
                <div class="flex items-center justify-center w-24 h-24 mx-auto mb-4 bg-gray-100 rounded-full dark:bg-gray-700">
                    <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                </div>
                <h3 class="mb-2 text-lg font-medium text-gray-900 dark:text-white">Tidak Ada Hasil</h3>
                <p class="text-gray-500 dark:text-gray-400">Tidak ditemukan data yang sesuai dengan pencarian Anda.</p>
            `;
                                    orgTree.appendChild(message);
                                } else if (!show && existingMessage) {
                                    existingMessage.remove();
                                }
                            }

                            // Polyfill for roundRect if not supported
                            if (!CanvasRenderingContext2D.prototype.roundRect) {
                                CanvasRenderingContext2D.prototype.roundRect = function(x, y, width, height, radius) {
                                    if (typeof radius === 'number') {
                                        radius = {
                                            tl: radius,
                                            tr: radius,
                                            br: radius,
                                            bl: radius
                                        };
                                    } else {
                                        radius = {
                                            tl: 0,
                                            tr: 0,
                                            br: 0,
                                            bl: 0,
                                            ...radius
                                        };
                                    }

                                    this.moveTo(x + radius.tl, y);
                                    this.lineTo(x + width - radius.tr, y);
                                    this.quadraticCurveTo(x + width, y, x + width, y + radius.tr);
                                    this.lineTo(x + width, y + height - radius.br);
                                    this.quadraticCurveTo(x + width, y + height, x + width - radius.br, y + height);
                                    this.lineTo(x + radius.bl, y + height);
                                    this.quadraticCurveTo(x, y + height, x, y + height - radius.bl);
                                    this.lineTo(x, y + radius.tl);
                                    this.quadraticCurveTo(x, y, x + radius.tl, y);
                                    this.closePath();
                                };
                            }
                        });
        </script>
    </div>
</x-filament-panels::page>
